# ERPNext PostgreSQL Migration Strategy

## Overview
This document outlines the systematic approach to migrate ERPNext from MySQL/MariaDB to PostgreSQL for small-scale business deployment.

## Phase 1: Environment Setup ✓
- [x] PostgreSQL installation and configuration
- [x] Redis setup for caching
- [x] Bench CLI installation
- [x] Initial ERPNext setup with PostgreSQL

## Phase 2: Compatibility Assessment

### 2.1 Identify MySQL-Specific Code
Common MySQL-specific elements to find and convert:

#### Data Types
- `LONGTEXT` → `TEXT`
- `TINYINT(1)` → `BOOLEAN`
- `DATETIME` → `TIMESTAMP`
- `AUTO_INCREMENT` → `SERIAL`

#### Functions
- `CONCAT()` → `||` operator or `CONCAT()`
- `IFNULL()` → `COALESCE()`
- `DATE_FORMAT()` → `TO_CHAR()`
- `UNIX_TIMESTAMP()` → `EXTRACT(EPOCH FROM timestamp)`
- `FROM_UNIXTIME()` → `TO_TIMESTAMP()`

#### SQL Syntax
- `LIMIT offset, count` → `LIMIT count OFFSET offset`
- Backticks `` `column` `` → Double quotes `"column"`
- `INSERT IGNORE` → `INSERT ... ON CONFLICT DO NOTHING`

### 2.2 Search Strategy
```bash
# Find MySQL-specific functions
grep -r "IFNULL\|DATE_FORMAT\|UNIX_TIMESTAMP" apps/erpnext/

# Find data type definitions
grep -r "LONGTEXT\|TINYINT\|AUTO_INCREMENT" apps/erpnext/

# Find raw SQL queries
grep -r "frappe\.db\.sql\|frappe\.db\.multisql" apps/erpnext/
```

## Phase 3: Systematic Conversion

### 3.1 Priority Order
1. **Core Framework Files** - Database connection and ORM
2. **Authentication & Users** - Login, permissions, user management
3. **Accounting Module** - Chart of accounts, journal entries
4. **Sales & Purchase** - Invoices, orders, payments
5. **Inventory** - Stock entries, item management
6. **HR & Payroll** - Employee records, salary processing
7. **Manufacturing** - Work orders, BOM
8. **CRM & Projects** - Customer management, project tracking

### 3.2 Conversion Approach
For each module:
1. **Identify Issues** - Run tests to find failing queries
2. **Categorize Problems** - Data types, functions, or syntax
3. **Apply Fixes** - Use ORM, rewrite queries, or use multi_sql
4. **Test Thoroughly** - Unit tests and manual testing
5. **Document Changes** - Track all modifications

### 3.3 Code Modification Patterns

#### Pattern 1: Use Frappe ORM (Preferred)
```python
# Instead of raw SQL
frappe.db.sql("SELECT * FROM tabUser WHERE enabled = 1")

# Use ORM
frappe.get_all("User", filters={"enabled": 1})
```

#### Pattern 2: Use multi_sql for Complex Queries
```python
# For database-specific queries
result = frappe.db.multisql({
    'mariadb': """
        SELECT DATE_FORMAT(creation, '%Y-%m') as month, COUNT(*) as count
        FROM tabSales_Invoice 
        GROUP BY month
    """,
    'postgres': """
        SELECT TO_CHAR(creation, 'YYYY-MM') as month, COUNT(*) as count
        FROM "tabSales Invoice"
        GROUP BY month
    """
})
```

#### Pattern 3: Data Type Mapping
```python
# In doctype JSON files, update field types
{
    "fieldname": "is_active",
    "fieldtype": "Check",  # Maps to BOOLEAN in PostgreSQL
    "default": 0
}
```

## Phase 4: Testing Strategy

### 4.1 Automated Testing
```bash
# Run ERPNext test suite
bench --site erpnext-postgres.local run-tests

# Run specific module tests
bench --site erpnext-postgres.local run-tests --module erpnext.accounts
```

### 4.2 Manual Testing Checklist
- [ ] User login and authentication
- [ ] Company and fiscal year setup
- [ ] Chart of accounts creation
- [ ] Customer and supplier management
- [ ] Item and price list setup
- [ ] Sales invoice creation and submission
- [ ] Purchase order workflow
- [ ] Payment entry processing
- [ ] Stock entry and valuation
- [ ] Report generation

## Phase 5: Custom Payment Integration

### 5.1 Moniepoint Integration Points
- Payment Gateway doctype
- Payment Request handling
- Webhook processing
- Transaction logging
- Reconciliation workflows

### 5.2 Testing with Mock Servers
- Simulate Moniepoint API responses
- Test error scenarios
- Validate webhook handling
- Performance testing

## Risk Mitigation

### 1. Backup Strategy
- Regular database backups during development
- Git version control for all code changes
- Rollback procedures documented

### 2. Incremental Approach
- Fix one module at a time
- Test thoroughly before moving to next module
- Maintain working baseline

### 3. Documentation
- Track all changes made
- Document PostgreSQL-specific configurations
- Create deployment guides

## Success Metrics
- All ERPNext modules working with PostgreSQL
- Payment gateway integration functional
- Performance comparable to MySQL version
- Clean, maintainable codebase
- Comprehensive documentation

## Timeline Estimate
- **Week 1-2**: Environment setup and compatibility assessment
- **Week 3-6**: Core module conversion (Accounts, Sales, Purchase)
- **Week 7-8**: Remaining modules and testing
- **Week 9-10**: Payment gateway integration
- **Week 11-12**: Final testing and documentation
