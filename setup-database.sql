-- ERPNext PostgreSQL Database Setup Script
-- Run this script as the postgres superuser

-- Create database for ERPNext
CREATE DATABASE erpnext_db;

-- Create user for ERPNext
CREATE USER erpnext_user WITH PASSWORD 'erpnext_password_2024';

-- Grant privileges to the user
GRANT ALL PRIVILEGES ON DATABASE erpnext_db TO erpnext_user;

-- Allow user to create databases (needed for ERPNext)
ALTER USER erpnext_user CREATEDB;

-- Connect to the ERPNext database
\c erpnext_db;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO erpnext_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO erpnext_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO erpnext_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO erpnext_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO erpnext_user;

-- Display confirmation
SELECT 'Database setup completed successfully!' as status;
