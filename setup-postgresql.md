# PostgreSQL Installation Guide for ERPNext

## Step 1: Install PostgreSQL on Windows

### Option A: Using PostgreSQL Installer (Recommended)
1. Download PostgreSQL from: https://www.postgresql.org/download/windows/
2. Choose version 14 or 15 (compatible with ERPNext)
3. Run the installer with these settings:
   - Port: 5432 (default)
   - Superuser password: Create a strong password (remember this!)
   - Locale: Default
   - Install pgAdmin 4: Yes (for database management)

### Option B: Using Chocolatey (if you have it)
```powershell
choco install postgresql
```

## Step 2: Verify PostgreSQL Installation
```powershell
# Check if PostgreSQL is running
Get-Service postgresql*

# Test connection (replace 'your_password' with your actual password)
psql -U postgres -h localhost
```

## Step 3: Create ERPNext Database and User
```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database for ERPNext
CREATE DATABASE erpnext_db;

-- Create user for ERPNext
CREATE USER erpnext_user WITH PASSWORD 'your_secure_password';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON DATABASE erpnext_db TO erpnext_user;
ALTER USER erpnext_user CREATEDB;

-- Exit PostgreSQL
\q
```

## Step 4: Configure PostgreSQL for ERPNext
Edit `postgresql.conf` (usually in `C:\Program Files\PostgreSQL\15\data\`):

```ini
# Add these settings for ERPNext
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

Edit `pg_hba.conf` to allow local connections:
```
# Add this line for local development
host    all             all             127.0.0.1/32            md5
```

## Step 5: Restart PostgreSQL Service
```powershell
Restart-Service postgresql*
```

## Next Steps
After PostgreSQL is installed and configured, we'll:
1. Install Redis
2. Set up Bench CLI
3. Clone and modify ERPNext for PostgreSQL compatibility
