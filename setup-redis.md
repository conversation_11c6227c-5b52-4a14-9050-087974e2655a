# Redis Installation Guide for ERPNext

## Step 1: Install Redis on Windows

### Option A: Using Redis for Windows (Recommended)
1. Download from: https://github.com/microsoftarchive/redis/releases
2. Download the latest .msi file (e.g., Redis-x64-3.0.504.msi)
3. Run the installer with default settings
4. Redis will be installed as a Windows service

### Option B: Using Chocolatey
```powershell
choco install redis-64
```

### Option C: Using WSL2 (Advanced)
If you have WSL2 installed:
```bash
# In WSL2 terminal
sudo apt update
sudo apt install redis-server
sudo service redis-server start
```

## Step 2: Verify Redis Installation
```powershell
# Check if Redis service is running
Get-Service redis*

# Test Redis connection
redis-cli ping
# Should return: PONG
```

## Step 3: Configure Redis for ERPNext
Create or edit Redis configuration file (`redis.conf`):

```ini
# Basic ERPNext Redis configuration
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 0
loglevel notice
databases 16
save 900 1
save 300 10
save 60 10000
maxmemory-policy allkeys-lru
```

## Step 4: Start Redis Service
```powershell
# Start Redis service
Start-Service redis

# Or restart if already running
Restart-Service redis
```

## Step 5: Test Redis Connection
```powershell
redis-cli
# In Redis CLI:
127.0.0.1:6379> ping
PONG
127.0.0.1:6379> set test "Hello ERPNext"
OK
127.0.0.1:6379> get test
"Hello ERPNext"
127.0.0.1:6379> exit
```

## Next Steps
After Redis is installed and running, we'll proceed with Bench CLI installation and ERPNext setup.
