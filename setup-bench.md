# Bench CLI Installation and ERPNext Setup with PostgreSQL

## Step 1: Install Bench CLI

### Prerequisites Check
```powershell
# Verify Python version (should be 3.8+)
python --version

# Verify Node.js version (should be 14+)
node --version

# Verify Git
git --version
```

### Install Bench
```powershell
# Install bench using pip
pip install frappe-bench

# Verify installation
bench --version
```

## Step 2: Initialize Bench with PostgreSQL Support

```powershell
# Create a new bench directory
bench init --frappe-branch version-15 --python python3 frappe-bench

# Navigate to bench directory
cd frappe-bench

# Create a new site with PostgreSQL
bench new-site erpnext-postgres.local --db-type postgres --db-host localhost --db-port 5432 --db-name erpnext_db --db-user erpnext_user --db-password your_secure_password
```

## Step 3: Install ERPNext App

```powershell
# Get ERPNext app
bench get-app erpnext --branch version-15

# Install ERPNext on the site
bench --site erpnext-postgres.local install-app erpnext
```

## Step 4: Configure Development Environment

```powershell
# Enable developer mode
bench --site erpnext-postgres.local set-config developer_mode 1

# Set up hosts file (run as Administrator)
# Add this line to C:\Windows\System32\drivers\etc\hosts:
# 127.0.0.1    erpnext-postgres.local
```

## Step 5: Start Development Server

```powershell
# Start the development server
bench start
```

## Expected Issues and Solutions

### Issue 1: PostgreSQL Connection Errors
If you get connection errors, check:
1. PostgreSQL service is running
2. Database credentials are correct
3. pg_hba.conf allows local connections

### Issue 2: MySQL-specific Queries Failing
This is expected! We'll systematically fix these in the next phase.

### Issue 3: Permission Errors
Run PowerShell as Administrator for initial setup.

## Next Steps
Once the basic setup is complete, we'll:
1. Analyze ERPNext codebase for MySQL dependencies
2. Create a systematic migration plan
3. Start converting queries to PostgreSQL-compatible format
